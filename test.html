<!doctype html>
<html
        lang="en"
        style="
                margin: 0;
                padding: 0;
                min-height: 100%;
                display: flex;
                flex-direction: column;
        "
>
        <head>
                <meta charset="utf-8" />
                <meta
                        name="viewport"
                        content="width=device-width, initial-scale=1, shrink-to-fit=no"
                />

                <style>
                        :root {
                                --bg: white;
                                --col:   #27272a;
                                --bg-dark: #0f0f11;
                                --col-dark: #f4f4f5;
                        }


                        body {
                                background: var(--bg);
                                color: var(--col);
                                font-family: Arial, Helvetica, sans-serif;
                        }

                        @media (prefers-color-scheme: dark) {
                                body {
                                        background: var(--bg-dark);
                                        color: var(--col-dark);
                                }
                        }
                </style>

                <meta property="og:url" content="https://gradio.app/" />
                <meta property="og:type" content="website" />
                <meta property="og:image" content="" />
                <meta property="og:title" content="Evalscope Dashboard" />
                <meta
                        property="og:description"
                        content=""
                />
                <meta name="twitter:card" content="summary_large_image" />
                <meta name="twitter:creator" content="@teamGradio" />
                <meta name="twitter:title" content="Evalscope Dashboard" />
                <meta
                        name="twitter:description"
                        content=""
                />
                <meta name="twitter:image" content="" />

                <script data-gradio-mode>
                        window.__gradio_mode__ = "app";
                        window.iFrameResizer = {
                                heightCalculationMethod: "taggedElement"
                        };
                        window.parent?.postMessage(
                                { type: "SET_SCROLLING", enabled: false },
                                "*"
                        );
                </script>

                <script>window.gradio_config = {"version":"5.4.0","api_prefix":"/gradio_api","mode":"blocks","app_id":9753178453895308253,"dev_mode":false,"analytics_enabled":true,"components":[{"id":1,"type":"html","props":{"value":"\u003ch1 style=\"text-align: left;\"\u003e📈 EvalScope 看板 (v0.17.1)\u003c/h1\u003e","show_label":false,"visible":true,"elem_classes":[],"name":"html","_selectable":false},"skip_api":false,"component_class_id":"9751038d05b1f9a0d9094a458ddcd444","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"\u003cp\u003eHello\u003c/p\u003e"},{"id":2,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":3,"type":"column","props":{"scale":0,"min_width":35,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":4,"type":"button","props":{"value":"\u003c","variant":"secondary","visible":true,"interactive":true,"elem_classes":[],"name":"button","_selectable":false},"skip_api":true,"component_class_id":"64c734442b45c8207d83b2190aaa13a8","key":null},{"id":5,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":6,"type":"html","props":{"value":"\u003ch3 style=\"text-align: left;\"\u003e喜欢\u003ca href=\"https://github.com/modelscope/evalscope\" target=\"_blank\"\u003eEvalScope\u003c/a\u003e就动动手指给我们加个star吧 🥺 \u003c/h3\u003e","show_label":false,"visible":true,"elem_classes":[],"name":"html","_selectable":false},"skip_api":false,"component_class_id":"9751038d05b1f9a0d9094a458ddcd444","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"\u003cp\u003eHello\u003c/p\u003e"},{"id":7,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":8,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":9,"type":"state","props":{"value":true,"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f986c360\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":10,"type":"markdown","props":{"value":"## 设置","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":11,"type":"textbox","props":{"value":"./outputs","lines":1,"max_lines":20,"placeholder":"./outputs","label":"报告根路径","show_label":true,"container":true,"min_width":160,"visible":true,"autofocus":false,"autoscroll":true,"elem_classes":[],"type":"text","rtl":false,"show_copy_button":false,"submit_btn":false,"stop_btn":false,"name":"textbox","_selectable":false},"skip_api":false,"component_class_id":"c7b3e3d8ab179c3c9e12c37f0350d75a","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"Hello!!"},{"id":12,"type":"dropdown","props":{"choices":[],"value":[],"type":"value","multiselect":true,"allow_custom_value":false,"filterable":true,"label":"请选择报告","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"dropdown","_selectable":false},"skip_api":false,"component_class_id":"1a60199a31af765094aa58ee2d827001","key":null,"api_info":{"type":"array","items":{"type":"string","enum":[]}},"api_info_as_input":{"type":"array","items":{"type":"string","enum":[]}},"api_info_as_output":{"type":"array","items":{"type":"string","enum":[]}},"example_inputs":[]},{"id":13,"type":"button","props":{"value":"加载并查看","variant":"secondary","visible":true,"interactive":true,"elem_classes":[],"name":"button","_selectable":false},"skip_api":true,"component_class_id":"64c734442b45c8207d83b2190aaa13a8","key":null},{"id":14,"type":"markdown","props":{"value":"### 请选择报告并点击`加载并查看`来查看数据","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":15,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":16,"type":"column","props":{"scale":5,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":17,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":18,"type":"markdown","props":{"value":"## 可视化","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":19,"type":"tabs","props":{"visible":true,"name":"tabs"},"skip_api":true,"component_class_id":"9d8f209cba707055a30557e39414588a","key":null},{"id":20,"type":"tabitem","props":{"label":"单模型","visible":true,"interactive":true,"name":"tab"},"skip_api":true,"component_class_id":"e704c99c56c995ffb378e10a62ee5d68","key":null},{"id":21,"type":"dropdown","props":{"choices":[],"type":"value","allow_custom_value":false,"filterable":true,"label":"选择报告","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"dropdown","_selectable":false},"skip_api":false,"component_class_id":"1a60199a31af765094aa58ee2d827001","key":null,"api_info":{"type":"string","enum":[]},"api_info_as_input":{"type":"string","enum":[]},"api_info_as_output":{"type":"string","enum":[]},"example_inputs":null},{"id":22,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f986c720\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":23,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f986c4a0\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":24,"type":"accordion","props":{"label":"任务配置","open":false,"visible":true,"name":"accordion"},"skip_api":true,"component_class_id":"06e882abbba85c70338688b7802424ab","key":null},{"id":25,"type":"json","props":{"show_label":true,"container":true,"min_width":160,"visible":true,"elem_classes":[],"open":false,"show_indices":false,"max_height":500,"name":"json","_selectable":false},"skip_api":false,"component_class_id":"4dfd18a1694027cfd0846e3e869e84b6","key":null,"api_info":{"type":{},"description":"any valid json"},"api_info_as_input":{"type":{},"description":"any valid json"},"api_info_as_output":{"type":{},"description":"any valid json"},"example_inputs":{"foo":"bar"}},{"id":26,"type":"state","props":{"value":[],"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f986c900\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":27,"type":"tabitem","props":{"label":"数据集概览","visible":true,"interactive":true,"name":"tab"},"skip_api":true,"component_class_id":"e704c99c56c995ffb378e10a62ee5d68","key":null},{"id":28,"type":"markdown","props":{"value":"### 数据集组成","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":29,"type":"plot","props":{"format":"webp","label":"数据集组成","show_label":true,"container":true,"scale":1,"min_width":160,"visible":true,"elem_classes":[],"name":"plot","_selectable":false},"skip_api":false,"component_class_id":"7fd6d72a338b382bc065959765a4249d","key":null,"api_info":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_input":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_output":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"example_inputs":null},{"id":30,"type":"markdown","props":{"value":"### 数据集分数","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":31,"type":"plot","props":{"format":"webp","label":"数据集分数","show_label":true,"container":true,"scale":1,"min_width":160,"visible":true,"elem_classes":[],"name":"plot","_selectable":false},"skip_api":false,"component_class_id":"7fd6d72a338b382bc065959765a4249d","key":null,"api_info":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_input":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_output":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"example_inputs":null},{"id":32,"type":"markdown","props":{"value":"### 数据集分数表","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":33,"type":"dataframe","props":{"value":{"headers":["1","2","3"],"data":[["","",""]],"metadata":null},"headers":["1","2","3"],"row_count":[1,"dynamic"],"col_count":[3,"dynamic"],"datatype":"str","type":"pandas","latex_delimiters":[{"left":"$$","right":"$$","display":true}],"show_label":true,"max_height":500,"min_width":160,"visible":true,"elem_classes":[],"wrap":false,"line_breaks":true,"column_widths":[],"name":"dataframe","_selectable":false},"skip_api":false,"component_class_id":"a4ba5b33ea99c8c40ed38f396c309c98","key":null,"api_info":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"api_info_as_input":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"api_info_as_output":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"example_inputs":{"headers":["a","b"],"data":[["foo","bar"]]}},{"id":34,"type":"tabitem","props":{"label":"数据集详情","visible":true,"interactive":true,"name":"tab"},"skip_api":true,"component_class_id":"e704c99c56c995ffb378e10a62ee5d68","key":null},{"id":35,"type":"radio","props":{"choices":[],"type":"value","label":"选择数据集","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"radio","_selectable":false},"skip_api":false,"component_class_id":"616332d65dc4948ac19143d1e2312502","key":null,"api_info":{"enum":[],"title":"Radio","type":"string"},"api_info_as_input":{"enum":[],"title":"Radio","type":"string"},"api_info_as_output":{"enum":[],"title":"Radio","type":"string"},"example_inputs":null},{"id":36,"type":"accordion","props":{"label":"报告智能分析","open":true,"visible":true,"name":"accordion"},"skip_api":true,"component_class_id":"06e882abbba85c70338688b7802424ab","key":null},{"id":37,"type":"markdown","props":{"value":"N/A","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":38,"type":"markdown","props":{"value":"### 数据集分数","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":39,"type":"plot","props":{"format":"webp","label":"数据集分数","show_label":true,"container":true,"scale":1,"min_width":160,"visible":true,"elem_classes":[],"name":"plot","_selectable":false},"skip_api":false,"component_class_id":"7fd6d72a338b382bc065959765a4249d","key":null,"api_info":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_input":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_output":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"example_inputs":null},{"id":40,"type":"markdown","props":{"value":"### 数据集分数表","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":41,"type":"dataframe","props":{"value":{"headers":["1","2","3"],"data":[["","",""]],"metadata":null},"headers":["1","2","3"],"row_count":[1,"dynamic"],"col_count":[3,"dynamic"],"datatype":"str","type":"pandas","latex_delimiters":[{"left":"$$","right":"$$","display":true}],"show_label":true,"max_height":500,"min_width":160,"visible":true,"elem_classes":[],"wrap":false,"line_breaks":true,"column_widths":[],"name":"dataframe","_selectable":false},"skip_api":false,"component_class_id":"a4ba5b33ea99c8c40ed38f396c309c98","key":null,"api_info":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"api_info_as_input":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"api_info_as_output":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"example_inputs":{"headers":["a","b"],"data":[["foo","bar"]]}},{"id":42,"type":"markdown","props":{"value":"### 模型预测","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":43,"type":"dropdown","props":{"choices":[],"type":"value","allow_custom_value":false,"filterable":true,"label":"选择子集","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"dropdown","_selectable":false},"skip_api":false,"component_class_id":"1a60199a31af765094aa58ee2d827001","key":null,"api_info":{"type":"string","enum":[]},"api_info_as_input":{"type":"string","enum":[]},"api_info_as_output":{"type":"string","enum":[]},"example_inputs":null},{"id":44,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":45,"type":"radio","props":{"choices":[["All","All"],["Pass","Pass"],["Fail","Fail"]],"value":"All","type":"value","label":"答案模式","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"radio","_selectable":false},"skip_api":false,"component_class_id":"616332d65dc4948ac19143d1e2312502","key":null,"api_info":{"enum":["All","Pass","Fail"],"title":"Radio","type":"string"},"api_info_as_input":{"enum":["All","Pass","Fail"],"title":"Radio","type":"string"},"api_info_as_output":{"enum":["All","Pass","Fail"],"title":"Radio","type":"string"},"example_inputs":"All"},{"id":46,"type":"number","props":{"value":0.99,"label":"分数阈值","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"step":1,"name":"number","_selectable":false},"skip_api":false,"component_class_id":"7a827b407a7ef46fb17dc1c9e3da5fc9","key":null,"api_info":{"type":"number"},"api_info_as_input":{"type":"number"},"api_info_as_output":{"type":"number"},"example_inputs":3},{"id":47,"type":"form","props":{"scale":2,"min_width":320,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":48,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f986c860\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":49,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f986c540\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":50,"type":"row","props":{"variant":"panel","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":51,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":52,"type":"markdown","props":{"value":"### *Counts*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":53,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":54,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":55,"type":"number","props":{"value":1,"label":"页码","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"minimum":1,"maximum":1,"step":1,"name":"number","_selectable":false},"skip_api":false,"component_class_id":"7a827b407a7ef46fb17dc1c9e3da5fc9","key":null,"api_info":{"type":"number"},"api_info_as_input":{"type":"number"},"api_info_as_output":{"type":"number"},"example_inputs":3},{"id":56,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":57,"type":"row","props":{"variant":"panel","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":58,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":59,"type":"markdown","props":{"value":"### *Score*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":60,"type":"code","props":{"value":"","language":"json","lines":5,"show_label":true,"container":true,"min_width":160,"visible":true,"elem_id":"score_text","elem_classes":[],"wrap_lines":false,"name":"code","_selectable":false},"skip_api":false,"component_class_id":"7d18f26bbc71b9f346de78cf9240dddc","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"print(\u0027Hello World\u0027)"},{"id":61,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":62,"type":"markdown","props":{"value":"### *Normalized Score*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":63,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_id":"score_text","elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":64,"type":"row","props":{"variant":"panel","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":65,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":66,"type":"markdown","props":{"value":"### *Gold*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":67,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_id":"gold_text","elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":68,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":69,"type":"markdown","props":{"value":"### *Pred*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":70,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_id":"pred_text","elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":71,"type":"row","props":{"variant":"panel","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":72,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":73,"type":"markdown","props":{"value":"### *Input*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":74,"type":"code","props":{"value":"","language":"json","lines":5,"show_label":true,"container":true,"min_width":160,"visible":true,"elem_id":"input_text","elem_classes":[],"wrap_lines":false,"name":"code","_selectable":false},"skip_api":false,"component_class_id":"7d18f26bbc71b9f346de78cf9240dddc","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"print(\u0027Hello World\u0027)"},{"id":75,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":76,"type":"markdown","props":{"value":"### *Generated*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":77,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_id":"generated_text","elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":78,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":79,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":80,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":81,"type":"tabs","props":{"visible":true,"name":"tabs"},"skip_api":true,"component_class_id":"9d8f209cba707055a30557e39414588a","key":null},{"id":82,"type":"tabitem","props":{"label":"多模型","visible":true,"interactive":true,"name":"tab"},"skip_api":true,"component_class_id":"e704c99c56c995ffb378e10a62ee5d68","key":null},{"id":83,"type":"dropdown","props":{"choices":[],"value":[],"type":"value","multiselect":true,"allow_custom_value":false,"filterable":true,"label":"请选择报告","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"dropdown","_selectable":false},"skip_api":false,"component_class_id":"1a60199a31af765094aa58ee2d827001","key":null,"api_info":{"type":"array","items":{"type":"string","enum":[]}},"api_info_as_input":{"type":"array","items":{"type":"string","enum":[]}},"api_info_as_output":{"type":"array","items":{"type":"string","enum":[]}},"example_inputs":[]},{"id":84,"type":"state","props":{"value":[],"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a7ac00\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":85,"type":"tabitem","props":{"label":"模型概览","visible":true,"interactive":true,"name":"tab"},"skip_api":true,"component_class_id":"e704c99c56c995ffb378e10a62ee5d68","key":null},{"id":86,"type":"markdown","props":{"value":"模型对比雷达","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":87,"type":"plot","props":{"format":"webp","show_label":true,"container":true,"min_width":160,"visible":true,"elem_classes":[],"name":"plot","_selectable":false},"skip_api":false,"component_class_id":"7fd6d72a338b382bc065959765a4249d","key":null,"api_info":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_input":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"api_info_as_output":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"example_inputs":null},{"id":88,"type":"markdown","props":{"value":"模型对比分数","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":89,"type":"dataframe","props":{"value":{"headers":["1","2","3"],"data":[["","",""]],"metadata":null},"headers":["1","2","3"],"row_count":[1,"dynamic"],"col_count":[3,"dynamic"],"datatype":"str","type":"pandas","latex_delimiters":[{"left":"$$","right":"$$","display":true}],"show_label":true,"max_height":500,"min_width":160,"visible":true,"elem_classes":[],"wrap":false,"line_breaks":true,"column_widths":[],"name":"dataframe","_selectable":false},"skip_api":false,"component_class_id":"a4ba5b33ea99c8c40ed38f396c309c98","key":null,"api_info":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"api_info_as_input":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"api_info_as_output":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"example_inputs":{"headers":["a","b"],"data":[["foo","bar"]]}},{"id":90,"type":"tabitem","props":{"label":"模型对比详情","visible":true,"interactive":true,"name":"tab"},"skip_api":true,"component_class_id":"e704c99c56c995ffb378e10a62ee5d68","key":null},{"id":91,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":92,"type":"dropdown","props":{"choices":[],"type":"value","allow_custom_value":false,"filterable":true,"label":"选择模型A","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"dropdown","_selectable":false},"skip_api":false,"component_class_id":"1a60199a31af765094aa58ee2d827001","key":null,"api_info":{"type":"string","enum":[]},"api_info_as_input":{"type":"string","enum":[]},"api_info_as_output":{"type":"string","enum":[]},"example_inputs":null},{"id":93,"type":"dropdown","props":{"choices":[],"type":"value","allow_custom_value":false,"filterable":true,"label":"选择模型B","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"dropdown","_selectable":false},"skip_api":false,"component_class_id":"1a60199a31af765094aa58ee2d827001","key":null,"api_info":{"type":"string","enum":[]},"api_info_as_input":{"type":"string","enum":[]},"api_info_as_output":{"type":"string","enum":[]},"example_inputs":null},{"id":94,"type":"form","props":{"scale":2,"min_width":320,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":95,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a7a8e0\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":96,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a7a520\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":97,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a7a160\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":98,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a79da0\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":99,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a799e0\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":100,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a79760\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":101,"type":"radio","props":{"choices":[],"type":"value","label":"选择数据集","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"radio","_selectable":false},"skip_api":false,"component_class_id":"616332d65dc4948ac19143d1e2312502","key":null,"api_info":{"enum":[],"title":"Radio","type":"string"},"api_info_as_input":{"enum":[],"title":"Radio","type":"string"},"api_info_as_output":{"enum":[],"title":"Radio","type":"string"},"example_inputs":null},{"id":102,"type":"markdown","props":{"value":"### 模型预测","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":103,"type":"dropdown","props":{"choices":[],"type":"value","allow_custom_value":false,"filterable":true,"label":"选择子集","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"dropdown","_selectable":false},"skip_api":false,"component_class_id":"1a60199a31af765094aa58ee2d827001","key":null,"api_info":{"type":"string","enum":[]},"api_info_as_input":{"type":"string","enum":[]},"api_info_as_output":{"type":"string","enum":[]},"example_inputs":null},{"id":104,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":105,"type":"radio","props":{"choices":[["All","All"],["Pass A \u0026 B","Pass A \u0026 B"],["Fail A \u0026 B","Fail A \u0026 B"],["Pass A, Fail B","Pass A, Fail B"],["Fail A, Pass B","Fail A, Pass B"]],"value":"All","type":"value","label":"答案模式","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"name":"radio","_selectable":false},"skip_api":false,"component_class_id":"616332d65dc4948ac19143d1e2312502","key":null,"api_info":{"enum":["All","Pass A \u0026 B","Fail A \u0026 B","Pass A, Fail B","Fail A, Pass B"],"title":"Radio","type":"string"},"api_info_as_input":{"enum":["All","Pass A \u0026 B","Fail A \u0026 B","Pass A, Fail B","Fail A, Pass B"],"title":"Radio","type":"string"},"api_info_as_output":{"enum":["All","Pass A \u0026 B","Fail A \u0026 B","Pass A, Fail B","Fail A, Pass B"],"title":"Radio","type":"string"},"example_inputs":"All"},{"id":106,"type":"number","props":{"value":0.99,"label":"分数阈值","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"step":1,"name":"number","_selectable":false},"skip_api":false,"component_class_id":"7a827b407a7ef46fb17dc1c9e3da5fc9","key":null,"api_info":{"type":"number"},"api_info_as_input":{"type":"number"},"api_info_as_output":{"type":"number"},"example_inputs":3},{"id":107,"type":"form","props":{"scale":2,"min_width":320,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":108,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f986c7c0\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":109,"type":"state","props":{"time_to_live":null,"delete_callback":"\u003cfunction State.__init__.\u003clocals\u003e.\u003clambda\u003e at 0x7f74f8a7ade0\u003e","name":"state","_selectable":false},"skip_api":true,"component_class_id":"c5447e43b92fc55a9ed344d950c4176e","key":null},{"id":110,"type":"row","props":{"variant":"panel","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":111,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":112,"type":"markdown","props":{"value":"### *Counts*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":113,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":114,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":115,"type":"number","props":{"value":1,"label":"页码","show_label":true,"container":true,"min_width":160,"interactive":true,"visible":true,"elem_classes":[],"minimum":1,"maximum":1,"step":1,"name":"number","_selectable":false},"skip_api":false,"component_class_id":"7a827b407a7ef46fb17dc1c9e3da5fc9","key":null,"api_info":{"type":"number"},"api_info_as_input":{"type":"number"},"api_info_as_output":{"type":"number"},"example_inputs":3},{"id":116,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":117,"type":"row","props":{"variant":"panel","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":118,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":119,"type":"markdown","props":{"value":"### *Input*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":120,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_id":"input_text","elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":121,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":122,"type":"markdown","props":{"value":"### *Gold Answer*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":123,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_id":"gold_text","elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":124,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":125,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":126,"type":"markdown","props":{"value":"### *Model A*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":127,"type":"column","props":{"scale":1,"min_width":320,"variant":"default","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":128,"type":"markdown","props":{"value":"### *Model B*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":129,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":130,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":131,"type":"markdown","props":{"value":"### *Score*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":132,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":133,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":134,"type":"markdown","props":{"value":"### *Score*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":135,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":136,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":137,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":138,"type":"markdown","props":{"value":"### *Normalized Score*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":139,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":140,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":141,"type":"markdown","props":{"value":"### *Normalized Score*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":142,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":143,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":144,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":145,"type":"markdown","props":{"value":"### *Prediction*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":146,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":147,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":148,"type":"markdown","props":{"value":"### *Prediction*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":149,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":150,"type":"row","props":{"variant":"default","visible":true,"equal_height":false,"show_progress":false,"name":"row"},"skip_api":true,"component_class_id":"c3d88a6fb3c4938d97094d01e584d889","key":null},{"id":151,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":152,"type":"markdown","props":{"value":"### *Generated*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":153,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":154,"type":"column","props":{"scale":1,"min_width":320,"variant":"panel","visible":true,"show_progress":false,"name":"column"},"skip_api":true,"component_class_id":"f2e4969b45fe8c10ee45a1cc38cc7aa4","key":null},{"id":155,"type":"markdown","props":{"value":"### *Generated*","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":156,"type":"markdown","props":{"value":"","show_label":false,"rtl":false,"latex_delimiters":[{"left":"$$","right":"$$","display":true},{"left":"$","right":"$","display":false},{"left":"\\(","right":"\\)","display":false},{"left":"\\[","right":"\\]","display":true}],"visible":true,"elem_classes":[],"sanitize_html":true,"line_breaks":false,"header_links":false,"show_copy_button":false,"container":false,"name":"markdown","_selectable":false},"skip_api":false,"component_class_id":"b82a08f76b716d3a497d5de8605f0d57","key":null,"api_info":{"type":"string"},"api_info_as_input":{"type":"string"},"api_info_as_output":{"type":"string"},"example_inputs":"# Hello!"},{"id":157,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":158,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":159,"type":"form","props":{"scale":0,"min_width":0,"name":"form"},"skip_api":true,"component_class_id":"4e8f697011e5903e4377323c3f0fac52","key":null},{"id":160,"type":"tabs","props":{"visible":true,"name":"tabs"},"skip_api":true,"component_class_id":"9d8f209cba707055a30557e39414588a","key":null}],"css":"","connect_heartbeat":true,"js":"","head":"","title":"Evalscope Dashboard","space_id":null,"enable_queue":true,"show_error":false,"show_api":true,"is_colab":false,"max_file_size":null,"stylesheets":["https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600\u0026display=swap"],"theme":"default","protocol":"sse_v3","body_css":{"body_background_fill":"white","body_text_color":"#27272a","body_background_fill_dark":"#0f0f11","body_text_color_dark":"#f4f4f5"},"fill_height":false,"fill_width":false,"theme_hash":"76ee63afdb6c2791ddf9b92428cb796885031b4a4f1259df434def0a7c3f9d63","layout":{"id":0,"children":[{"id":1},{"id":2,"children":[{"id":3,"children":[{"id":4}]},{"id":5,"children":[{"id":6}]}]},{"id":7,"children":[{"id":8,"children":[{"id":9},{"id":10},{"id":15,"children":[{"id":11},{"id":12}]},{"id":13},{"id":14}]},{"id":16,"children":[{"id":17,"children":[{"id":18},{"id":19,"children":[{"id":20,"children":[{"id":80,"children":[{"id":21}]},{"id":22},{"id":23},{"id":24,"children":[{"id":25}]},{"id":26},{"id":81,"children":[{"id":27,"children":[{"id":28},{"id":29},{"id":30},{"id":31},{"id":32},{"id":33}]},{"id":34,"children":[{"id":78,"children":[{"id":35}]},{"id":36,"children":[{"id":37}]},{"id":38},{"id":39},{"id":40},{"id":41},{"id":42},{"id":79,"children":[{"id":43}]},{"id":44,"children":[{"id":47,"children":[{"id":45},{"id":46}]}]},{"id":48},{"id":49},{"id":50,"children":[{"id":51,"children":[{"id":52},{"id":53}]},{"id":54,"children":[{"id":56,"children":[{"id":55}]}]}]},{"id":57,"children":[{"id":58,"children":[{"id":59},{"id":60}]},{"id":61,"children":[{"id":62},{"id":63}]}]},{"id":64,"children":[{"id":65,"children":[{"id":66},{"id":67}]},{"id":68,"children":[{"id":69},{"id":70}]}]},{"id":71,"children":[{"id":72,"children":[{"id":73},{"id":74}]},{"id":75,"children":[{"id":76},{"id":77}]}]}]}]}]},{"id":82,"children":[{"id":159,"children":[{"id":83}]},{"id":84},{"id":160,"children":[{"id":85,"children":[{"id":86},{"id":87},{"id":88},{"id":89}]},{"id":90,"children":[{"id":91,"children":[{"id":94,"children":[{"id":92},{"id":93}]}]},{"id":95},{"id":96},{"id":97},{"id":98},{"id":99},{"id":100},{"id":157,"children":[{"id":101}]},{"id":102},{"id":158,"children":[{"id":103}]},{"id":104,"children":[{"id":107,"children":[{"id":105},{"id":106}]}]},{"id":108},{"id":109},{"id":110,"children":[{"id":111,"children":[{"id":112},{"id":113}]},{"id":114,"children":[{"id":116,"children":[{"id":115}]}]}]},{"id":117,"children":[{"id":118,"children":[{"id":119},{"id":120}]},{"id":121,"children":[{"id":122},{"id":123}]}]},{"id":124,"children":[{"id":125,"children":[{"id":126}]},{"id":127,"children":[{"id":128}]}]},{"id":129,"children":[{"id":130,"children":[{"id":131},{"id":132}]},{"id":133,"children":[{"id":134},{"id":135}]}]},{"id":136,"children":[{"id":137,"children":[{"id":138},{"id":139}]},{"id":140,"children":[{"id":141},{"id":142}]}]},{"id":143,"children":[{"id":144,"children":[{"id":145},{"id":146}]},{"id":147,"children":[{"id":148},{"id":149}]}]},{"id":150,"children":[{"id":151,"children":[{"id":152},{"id":153}]},{"id":154,"children":[{"id":155},{"id":156}]}]}]}]}]}]}]}]}]}]},"dependencies":[{"id":0,"targets":[[12,"focus"]],"inputs":[11],"outputs":[12],"backend_fn":true,"js":null,"queue":true,"api_name":"update_dropdown_choices","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"once","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":1,"targets":[[21,"change"]],"inputs":[11,21],"outputs":[26,25,35,22,23],"backend_fn":true,"js":null,"queue":true,"api_name":"update_single_report_data","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":2,"targets":[[26,"change"]],"inputs":[26],"outputs":[31,33,29],"backend_fn":true,"js":null,"queue":true,"api_name":"update_single_report_score","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":3,"targets":[[35,"change"],[26,"change"]],"inputs":[35,26],"outputs":[39,41,43,48,37],"backend_fn":true,"js":null,"queue":true,"api_name":"update_single_report_dataset","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":4,"targets":[[43,"change"]],"inputs":[22,23,35,43],"outputs":[48,55],"backend_fn":true,"js":null,"queue":true,"api_name":"update_single_report_subset","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":5,"targets":[[48,"change"],[45,"change"],[46,"change"]],"inputs":[48,45,46],"outputs":[49,55,53],"backend_fn":true,"js":null,"queue":true,"api_name":"filter_data","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":6,"targets":[[49,"change"],[55,"change"]],"inputs":[49,55,46],"outputs":[74,77,67,70,60,63],"backend_fn":true,"js":null,"queue":true,"api_name":"update_table_components","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":7,"targets":[[83,"change"]],"inputs":[11,83],"outputs":[84,87,89,92,93],"backend_fn":true,"js":null,"queue":true,"api_name":"update_multi_report_data","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":8,"targets":[[92,"change"],[93,"change"]],"inputs":[11,92,93],"outputs":[95,96,97,98,99,100,101],"backend_fn":true,"js":null,"queue":true,"api_name":"update_selected_models","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":9,"targets":[[101,"change"]],"inputs":[101,95,96],"outputs":[103,108],"backend_fn":true,"js":null,"queue":true,"api_name":"update_dataset_comparison","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":10,"targets":[[103,"change"]],"inputs":[97,98,99,100,101,103],"outputs":[108,115],"backend_fn":true,"js":null,"queue":true,"api_name":"update_comparison_data","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":11,"targets":[[108,"change"],[105,"change"],[106,"change"]],"inputs":[108,105,106],"outputs":[109,115,113],"backend_fn":true,"js":null,"queue":true,"api_name":"filter_comparison_data","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":12,"targets":[[109,"change"],[115,"change"],[92,"change"],[93,"change"]],"inputs":[109,115,106,92,93,99,100],"outputs":[120,123,153,146,132,139,156,149,135,142],"backend_fn":true,"js":null,"queue":true,"api_name":"update_comparison_display","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"always_last","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":13,"targets":[[13,"click"]],"inputs":[12],"outputs":[21,83],"backend_fn":true,"js":null,"queue":true,"api_name":"update_displays","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"once","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null},{"id":14,"targets":[[4,"click"]],"inputs":[9],"outputs":[8,9,4],"backend_fn":true,"js":null,"queue":true,"api_name":"toggle_sidebar","scroll_to_output":false,"show_progress":"full","batch":false,"max_batch_size":4,"cancels":[],"types":{"generator":false,"cancel":false},"collects_event_data":false,"trigger_after":null,"trigger_only_on_success":false,"trigger_mode":"once","show_api":true,"zerogpu":false,"rendered_in":null,"connection":"sse","time_limit":null,"stream_every":0.5,"like_user_message":false,"event_specific_args":null}],"root":"http://0.0.0.0:7860","username":null};</script><script>window.gradio_api_info = {"named_endpoints":{"/update_dropdown_choices":{"parameters":[{"label":"报告根路径","parameter_name":"root_path","parameter_has_default":true,"parameter_default":"./outputs","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Textbox","example_input":"Hello!!"}],"returns":[{"label":"请选择报告","type":{"type":"array","items":{"type":"string","enum":[]}},"python_type":{"type":"List[Literal[]]","description":""},"component":"Dropdown"}],"show_api":true},"/update_single_report_data":{"parameters":[{"label":"报告根路径","parameter_name":"root_path","parameter_has_default":true,"parameter_default":"./outputs","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Textbox","example_input":"Hello!!"},{"label":"选择报告","parameter_name":"report_name","parameter_has_default":false,"parameter_default":null,"type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown","example_input":null}],"returns":[{"label":"value_25","type":{"type":{},"description":"any valid json"},"python_type":{"type":"Dict[Any, Any]","description":"any valid json"},"component":"Json"},{"label":"选择数据集","type":{"enum":[],"title":"Radio","type":"string"},"python_type":{"type":"Literal[]","description":""},"component":"Radio"}],"show_api":true},"/update_single_report_score":{"parameters":[],"returns":[{"label":"数据集分数","type":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"python_type":{"type":"Dict(type: Literal[\u0027altair\u0027, \u0027bokeh\u0027, \u0027plotly\u0027, \u0027matplotlib\u0027], plot: str)","description":""},"component":"Plot"},{"label":"value_33","type":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"python_type":{"type":"Dict(headers: List[str], data: List[List[Any]], metadata: Dict(str, List[Any] | None) | None)","description":""},"component":"Dataframe"},{"label":"数据集组成","type":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"python_type":{"type":"Dict(type: Literal[\u0027altair\u0027, \u0027bokeh\u0027, \u0027plotly\u0027, \u0027matplotlib\u0027], plot: str)","description":""},"component":"Plot"}],"show_api":true},"/update_single_report_dataset":{"parameters":[{"label":"选择数据集","parameter_name":"dataset_name","parameter_has_default":false,"parameter_default":null,"type":{"enum":[],"title":"Radio","type":"string"},"python_type":{"type":"Literal[]","description":""},"component":"Radio","example_input":null}],"returns":[{"label":"数据集分数","type":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"python_type":{"type":"Dict(type: Literal[\u0027altair\u0027, \u0027bokeh\u0027, \u0027plotly\u0027, \u0027matplotlib\u0027], plot: str)","description":""},"component":"Plot"},{"label":"value_41","type":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"python_type":{"type":"Dict(headers: List[str], data: List[List[Any]], metadata: Dict(str, List[Any] | None) | None)","description":""},"component":"Dataframe"},{"label":"选择子集","type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown"},{"label":"value_37","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"}],"show_api":true},"/update_single_report_subset":{"parameters":[{"label":"选择数据集","parameter_name":"dataset_name","parameter_has_default":false,"parameter_default":null,"type":{"enum":[],"title":"Radio","type":"string"},"python_type":{"type":"Literal[]","description":""},"component":"Radio","example_input":null},{"label":"选择子集","parameter_name":"subset_name","parameter_has_default":false,"parameter_default":null,"type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown","example_input":null}],"returns":[{"label":"页码","type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number"}],"show_api":true},"/filter_data":{"parameters":[{"label":"答案模式","parameter_name":"answer_mode","parameter_has_default":true,"parameter_default":"All","type":{"enum":["All","Pass","Fail"],"title":"Radio","type":"string"},"python_type":{"type":"Literal[\u0027All\u0027, \u0027Pass\u0027, \u0027Fail\u0027]","description":""},"component":"Radio","example_input":"All"},{"label":"分数阈值","parameter_name":"score_threshold","parameter_has_default":true,"parameter_default":0.99,"type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number","example_input":3}],"returns":[{"label":"页码","type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number"},{"label":"value_53","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"}],"show_api":true},"/update_table_components":{"parameters":[{"label":"页码","parameter_name":"page_number","parameter_has_default":true,"parameter_default":1,"type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number","example_input":3},{"label":"分数阈值","parameter_name":"score_threshold","parameter_has_default":true,"parameter_default":0.99,"type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number","example_input":3}],"returns":[{"label":"value_74","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Code"},{"label":"value_77","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_67","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_70","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_60","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Code"},{"label":"value_63","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"}],"show_api":true},"/update_multi_report_data":{"parameters":[{"label":"报告根路径","parameter_name":"root_path","parameter_has_default":true,"parameter_default":"./outputs","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Textbox","example_input":"Hello!!"},{"label":"请选择报告","parameter_name":"multi_report_names","parameter_has_default":true,"parameter_default":[],"type":{"type":"array","items":{"type":"string","enum":[]}},"python_type":{"type":"List[Literal[]]","description":""},"component":"Dropdown","example_input":[]}],"returns":[{"label":"value_87","type":{"properties":{"type":{"enum":["altair","bokeh","plotly","matplotlib"],"title":"Type","type":"string"},"plot":{"title":"Plot","type":"string"}},"required":["type","plot"],"title":"PlotData","type":"object","additional_description":null},"python_type":{"type":"Dict(type: Literal[\u0027altair\u0027, \u0027bokeh\u0027, \u0027plotly\u0027, \u0027matplotlib\u0027], plot: str)","description":""},"component":"Plot"},{"label":"value_89","type":{"properties":{"headers":{"items":{"type":"string"},"title":"Headers","type":"array"},"data":{"items":{"items":{},"type":"array"},"title":"Data","type":"array"},"metadata":{"anyOf":[{"additionalProperties":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}]},"type":"object"},{"type":"null"}],"default":null,"title":"Metadata"}},"required":["headers","data"],"title":"DataframeData","type":"object","additional_description":null},"python_type":{"type":"Dict(headers: List[str], data: List[List[Any]], metadata: Dict(str, List[Any] | None) | None)","description":""},"component":"Dataframe"},{"label":"选择模型A","type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown"},{"label":"选择模型B","type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown"}],"show_api":true},"/update_selected_models":{"parameters":[{"label":"报告根路径","parameter_name":"root_path","parameter_has_default":true,"parameter_default":"./outputs","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Textbox","example_input":"Hello!!"},{"label":"选择模型A","parameter_name":"model_a","parameter_has_default":false,"parameter_default":null,"type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown","example_input":null},{"label":"选择模型B","parameter_name":"model_b","parameter_has_default":false,"parameter_default":null,"type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown","example_input":null}],"returns":[{"label":"选择数据集","type":{"enum":[],"title":"Radio","type":"string"},"python_type":{"type":"Literal[]","description":""},"component":"Radio"}],"show_api":true},"/update_dataset_comparison":{"parameters":[{"label":"选择数据集","parameter_name":"dataset_name","parameter_has_default":false,"parameter_default":null,"type":{"enum":[],"title":"Radio","type":"string"},"python_type":{"type":"Literal[]","description":""},"component":"Radio","example_input":null}],"returns":[{"label":"选择子集","type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown"}],"show_api":true},"/update_comparison_data":{"parameters":[{"label":"选择数据集","parameter_name":"dataset_name","parameter_has_default":false,"parameter_default":null,"type":{"enum":[],"title":"Radio","type":"string"},"python_type":{"type":"Literal[]","description":""},"component":"Radio","example_input":null},{"label":"选择子集","parameter_name":"subset_name","parameter_has_default":false,"parameter_default":null,"type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown","example_input":null}],"returns":[{"label":"页码","type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number"}],"show_api":true},"/filter_comparison_data":{"parameters":[{"label":"答案模式","parameter_name":"answer_mode","parameter_has_default":true,"parameter_default":"All","type":{"enum":["All","Pass A \u0026 B","Fail A \u0026 B","Pass A, Fail B","Fail A, Pass B"],"title":"Radio","type":"string"},"python_type":{"type":"Literal[\u0027All\u0027, \u0027Pass A \u0026 B\u0027, \u0027Fail A \u0026 B\u0027, \u0027Pass A, Fail B\u0027, \u0027Fail A, Pass B\u0027]","description":""},"component":"Radio","example_input":"All"},{"label":"分数阈值","parameter_name":"score_threshold","parameter_has_default":true,"parameter_default":0.99,"type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number","example_input":3}],"returns":[{"label":"页码","type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number"},{"label":"value_113","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"}],"show_api":true},"/update_comparison_display":{"parameters":[{"label":"页码","parameter_name":"page_number","parameter_has_default":true,"parameter_default":1,"type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number","example_input":3},{"label":"分数阈值","parameter_name":"score_threshold","parameter_has_default":true,"parameter_default":0.99,"type":{"type":"number"},"python_type":{"type":"float","description":""},"component":"Number","example_input":3},{"label":"选择模型A","parameter_name":"model_a_select","parameter_has_default":false,"parameter_default":null,"type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown","example_input":null},{"label":"选择模型B","parameter_name":"model_b_select","parameter_has_default":false,"parameter_default":null,"type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown","example_input":null}],"returns":[{"label":"value_120","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_123","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_153","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_146","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_132","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_139","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_156","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_149","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_135","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"},{"label":"value_142","type":{"type":"string"},"python_type":{"type":"str","description":""},"component":"Markdown"}],"show_api":true},"/update_displays":{"parameters":[{"label":"请选择报告","parameter_name":"reports_dropdown","parameter_has_default":true,"parameter_default":[],"type":{"type":"array","items":{"type":"string","enum":[]}},"python_type":{"type":"List[Literal[]]","description":""},"component":"Dropdown","example_input":[]}],"returns":[{"label":"选择报告","type":{"type":"string","enum":[]},"python_type":{"type":"Literal[]","description":""},"component":"Dropdown"},{"label":"请选择报告","type":{"type":"array","items":{"type":"string","enum":[]}},"python_type":{"type":"List[Literal[]]","description":""},"component":"Dropdown"}],"show_api":true},"/toggle_sidebar":{"parameters":[],"returns":[],"show_api":true}},"unnamed_endpoints":{}};</script>

                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link
                        rel="preconnect"
                        href="https://fonts.gstatic.com"
                        crossorigin="anonymous"
                />
                <script
                        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.1/iframeResizer.contentWindow.min.js"
                        async
                ></script>
                <script type="module" crossorigin src="./assets/index-D1QgYocY.js"></script>
                <link rel="stylesheet" crossorigin href="./assets/index-8mjQrEVj.css">
        </head>

        <body
                style="
                        width: 100%;
                        margin: 0;
                        padding: 0;
                        display: flex;
                        flex-direction: column;
                        flex-grow: 1;
                "
        >
                <gradio-app
                        control_page_title="true"
                        embed="false"
                        eager="true"
                        style="display: flex; flex-direction: column; flex-grow: 1"
                >
                </gradio-app>
                <script>
                        const ce = document.getElementsByTagName("gradio-app");

                        if (ce[0]) {
                                ce[0].addEventListener("domchange", () => {
                                        document.body.style.padding = "0";
                                });
                                document.body.style.padding = "0";
                        }
                </script>
        </body>