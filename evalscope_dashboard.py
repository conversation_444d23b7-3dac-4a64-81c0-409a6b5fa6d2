#!/usr/bin/env python3
"""
EvalScope Dashboard - 模型评估可视化看板
"""

import gradio as gr
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import json
import os
from pathlib import Path

class EvalScopeDashboard:
    def __init__(self):
        self.reports_data = {}
        self.current_reports = []
        
    def load_reports(self, reports_path):
        """加载报告数据"""
        try:
            reports_path = Path(reports_path)
            if not reports_path.exists():
                return [], "报告路径不存在"
            
            # 模拟加载报告文件
            report_files = []
            for file_path in reports_path.glob("*.json"):
                report_files.append(file_path.name)
            
            if not report_files:
                # 创建示例数据
                sample_data = {
                    "model_name": "示例模型",
                    "datasets": {
                        "数学推理": {"score": 0.85, "total": 100, "correct": 85},
                        "阅读理解": {"score": 0.78, "total": 150, "correct": 117},
                        "代码生成": {"score": 0.92, "total": 80, "correct": 74}
                    },
                    "overall_score": 0.85
                }
                report_files = ["示例报告.json"]
                self.reports_data["示例报告.json"] = sample_data
            
            return report_files, f"找到 {len(report_files)} 个报告文件"
            
        except Exception as e:
            return [], f"加载报告时出错: {str(e)}"
    
    def create_dataset_composition_chart(self, selected_reports):
        """创建数据集组成图表"""
        if not selected_reports:
            return None
        
        # 示例数据
        datasets = ["数学推理", "阅读理解", "代码生成", "常识推理"]
        counts = [100, 150, 80, 120]
        
        fig = px.pie(
            values=counts, 
            names=datasets,
            title="数据集组成分布"
        )
        fig.update_layout(
            font=dict(size=14),
            showlegend=True
        )
        return fig
    
    def create_dataset_scores_chart(self, selected_reports):
        """创建数据集分数图表"""
        if not selected_reports:
            return None
        
        # 示例数据
        datasets = ["数学推理", "阅读理解", "代码生成", "常识推理"]
        scores = [0.85, 0.78, 0.92, 0.73]
        
        fig = px.bar(
            x=datasets,
            y=scores,
            title="各数据集评分",
            labels={"x": "数据集", "y": "分数"}
        )
        fig.update_layout(
            yaxis=dict(range=[0, 1]),
            font=dict(size=14)
        )
        return fig
    
    def create_scores_table(self, selected_reports):
        """创建分数表格"""
        if not selected_reports:
            return pd.DataFrame()
        
        # 示例数据
        data = {
            "数据集": ["数学推理", "阅读理解", "代码生成", "常识推理"],
            "总题数": [100, 150, 80, 120],
            "正确数": [85, 117, 74, 88],
            "准确率": [0.85, 0.78, 0.92, 0.73],
            "难度": ["困难", "中等", "简单", "中等"]
        }
        return pd.DataFrame(data)
    
    def create_model_comparison_radar(self, selected_reports):
        """创建模型对比雷达图"""
        if len(selected_reports) < 2:
            return None
        
        categories = ["数学推理", "阅读理解", "代码生成", "常识推理", "逻辑推理"]
        
        fig = go.Figure()
        
        # 模型A数据
        model_a_scores = [0.85, 0.78, 0.92, 0.73, 0.80]
        fig.add_trace(go.Scatterpolar(
            r=model_a_scores,
            theta=categories,
            fill='toself',
            name='模型A'
        ))
        
        # 模型B数据
        model_b_scores = [0.82, 0.85, 0.88, 0.79, 0.75]
        fig.add_trace(go.Scatterpolar(
            r=model_b_scores,
            theta=categories,
            fill='toself',
            name='模型B'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )),
            showlegend=True,
            title="模型对比雷达图"
        )
        return fig

def create_dashboard():
    """创建Gradio界面"""
    dashboard = EvalScopeDashboard()
    
    with gr.Blocks(title="📈 EvalScope 看板 (v0.17.1)") as demo:
        gr.HTML("<h1 style='text-align: left;'>📈 EvalScope 看板 (v0.17.1)</h1>")
        
        with gr.Row():
            with gr.Column(scale=0, min_width=35):
                back_btn = gr.Button("<", variant="secondary")
            
            with gr.Column(scale=1, min_width=320):
                gr.HTML("<h3 style='text-align: left;'>喜欢<a href='https://github.com/modelscope/evalscope' target='_blank'>EvalScope</a>就动动手指给我们加个star吧 🥺 </h3>")
        
        with gr.Row():
            with gr.Column(scale=1, min_width=320):
                gr.Markdown("## 设置")
                
                reports_path = gr.Textbox(
                    value="./outputs",
                    label="报告根路径",
                    placeholder="./outputs"
                )
                
                reports_dropdown = gr.Dropdown(
                    choices=[],
                    value=[],
                    multiselect=True,
                    label="请选择报告",
                    interactive=True
                )
                
                load_btn = gr.Button("加载并查看", variant="secondary")
                
                status_text = gr.Markdown("### 请选择报告并点击`加载并查看`来查看数据")
            
            with gr.Column(scale=5, min_width=320):
                gr.Markdown("## 可视化")
                
                with gr.Tabs():
                    with gr.TabItem("单模型"):
                        single_report_dropdown = gr.Dropdown(
                            choices=[],
                            label="选择报告",
                            interactive=True
                        )
                        
                        with gr.Accordion("任务配置", open=False):
                            config_json = gr.JSON(label="配置信息")
                    
                    with gr.TabItem("数据集概览"):
                        gr.Markdown("### 数据集组成")
                        dataset_composition_plot = gr.Plot(label="数据集组成")
                        
                        gr.Markdown("### 数据集分数")
                        dataset_scores_plot = gr.Plot(label="数据集分数")
                        
                        gr.Markdown("### 数据集分数表")
                        scores_table = gr.Dataframe(label="分数表")
                    
                    with gr.TabItem("多模型"):
                        multi_reports_dropdown = gr.Dropdown(
                            choices=[],
                            value=[],
                            multiselect=True,
                            label="请选择报告",
                            interactive=True
                        )
                        
                        with gr.TabItem("模型概览"):
                            gr.Markdown("模型对比雷达")
                            radar_plot = gr.Plot()
                            
                            gr.Markdown("模型对比分数")
                            comparison_table = gr.Dataframe()
        
        # 事件处理
        def load_reports_handler(path):
            reports, status = dashboard.load_reports(path)
            return (
                gr.Dropdown(choices=reports),  # reports_dropdown
                gr.Dropdown(choices=reports),  # single_report_dropdown  
                gr.Dropdown(choices=reports),  # multi_reports_dropdown
                f"### {status}"  # status_text
            )
        
        def update_visualizations(selected_reports):
            if not selected_reports:
                return None, None, pd.DataFrame(), None, pd.DataFrame()
            
            composition_chart = dashboard.create_dataset_composition_chart(selected_reports)
            scores_chart = dashboard.create_dataset_scores_chart(selected_reports)
            scores_df = dashboard.create_scores_table(selected_reports)
            radar_chart = dashboard.create_model_comparison_radar(selected_reports)
            comparison_df = pd.DataFrame({
                "模型": ["模型A", "模型B"],
                "总分": [0.84, 0.82],
                "数学推理": [0.85, 0.82],
                "阅读理解": [0.78, 0.85]
            })
            
            return composition_chart, scores_chart, scores_df, radar_chart, comparison_df
        
        load_btn.click(
            load_reports_handler,
            inputs=[reports_path],
            outputs=[reports_dropdown, single_report_dropdown, multi_reports_dropdown, status_text]
        )
        
        reports_dropdown.change(
            update_visualizations,
            inputs=[reports_dropdown],
            outputs=[dataset_composition_plot, dataset_scores_plot, scores_table, radar_plot, comparison_table]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_dashboard()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        inbrowser=True
    )
